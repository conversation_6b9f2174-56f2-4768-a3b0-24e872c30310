Namespace(task_name='GINGraphPooling', device=device(type='cuda', index=0), num_layers=5, graph_pooling='sum', emb_dim=128, drop_ratio=0.0, save_test=False, batch_size=2048, epochs=1000, weight_decay=1e-05, early_stop=10, num_workers=0, dataset_root='dataset', save_dir='saves\\GINGraphPooling', output_file=<_io.TextIOWrapper name='saves\\GINGraphPooling\\output' mode='a' encoding='cp936'>)
Namespace(task_name='GINGraphPooling', device=device(type='cuda', index=0), num_layers=5, graph_pooling='sum', emb_dim=128, drop_ratio=0.0, save_test=False, batch_size=2048, epochs=1000, weight_decay=1e-05, early_stop=10, num_workers=0, dataset_root='dataset', save_dir='saves\\GINGraphPooling', output_file=<_io.TextIOWrapper name='saves\\GINGraphPooling\\output' mode='a' encoding='cp936'>)
Namespace(task_name='GINGraphPooling', device=device(type='cuda', index=0), num_layers=5, graph_pooling='sum', emb_dim=128, drop_ratio=0.0, save_test=False, batch_size=2048, epochs=1000, weight_decay=1e-05, early_stop=10, num_workers=0, dataset_root='dataset', save_dir='saves\\GINGraphPooling', output_file=<_io.TextIOWrapper name='saves\\GINGraphPooling\\output' mode='a' encoding='cp936'>)
