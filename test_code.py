#!/usr/bin/env python3
"""
Test script to check if the modified code can run properly
"""

import sys
import os
sys.path.append('code')

try:
    print("Testing imports...")
    import torch
    import pandas as pd
    import numpy as np
    from rdkit import Chem
    from rdkit.Chem import AllChem
    print("✓ Basic imports successful")
    
    # Test data loading
    print("\nTesting data loading...")
    df = pd.read_csv('dataset/Chiral_AquaTox_scr.csv')
    print(f"✓ Data loaded: {len(df)} samples")
    print(f"✓ Columns: {list(df.columns)}")
    print(f"✓ AT distribution: {df['AT'].value_counts().to_dict()}")
    
    # Test SMILES processing
    print("\nTesting SMILES processing...")
    test_smiles = df['smiles'].iloc[0]
    print(f"Test SMILES: {test_smiles}")
    
    mol = Chem.MolFromSmiles(test_smiles)
    if mol is not None:
        print("✓ SMILES parsing successful")
    else:
        print("✗ SMILES parsing failed")
        
    # Test 3D conformer generation (just one sample)
    print("\nTesting 3D conformer generation...")
    try:
        new_mol = Chem.AddHs(mol)
        res = AllChem.EmbedMultipleConfs(new_mol, numConfs=1)
        if len(res) > 0:
            print("✓ 3D conformer generation successful")
        else:
            print("✗ 3D conformer generation failed")
    except Exception as e:
        print(f"✗ 3D conformer generation error: {e}")
    
    print("\n=== Basic tests completed ===")
    
except Exception as e:
    print(f"✗ Error during testing: {e}")
    import traceback
    traceback.print_exc()
