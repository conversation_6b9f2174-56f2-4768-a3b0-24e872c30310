# Retention-Time-Prediction-for-Chromatographic-Enantioseparation
The dataset, code, and software for the paper 'Retention Time Prediction for Chromatographic Enantioseparation by Quantile Geometry-enhanced Graph Neural Network'

#Please cite  
<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Retention time prediction for chromatographic enantioseparation by quantile geometry-enhanced graph neural network.   Nat. Commun. 14 (2023), doi:10.1038/s41467-023-38853-3.

#Our website that can predict retention times online without any code is available!  
Welcome to try:  
https://huggingface.co/spaces/woshixuhao/Chromatographic_Enantioseparation  
Our another work to predict the Rf value in TLC is also available!  
https://huggingface.co/spaces/woshixuhao/Rf_prediction  

#Entire programm can also be downloaded from google drive since some files are to big:
https://drive.google.com/file/d/1kzM2_pG-Ob_hNQ7ga2n6Ds2FXDeAppWx/view?usp=share_link

#Enviroment:
Python==3.7

#core package
RDKit==2020.09.1.0,
pytorch==1.11.0,
pyg==2.0.4,
mordred==1.2.0,
pandas==1.3.5,
