# AquaTox Classification using Modified GeoGNN

## 项目概述

本项目基于原始的手性色谱保留时间预测模型，修改为水生毒性二分类模型。使用 `Chiral_AquaTox_scr.csv` 数据集，预测分子的水生毒性（AT标签：0=无毒，1=有毒）。

## 原始项目信息

**原始论文**: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Retention time prediction for chromatographic enantioseparation by quantile geometry-enhanced graph neural network. Nat. Commun. 14 (2023), doi:10.1038/s41467-023-38853-3.

**在线预测网站**:
- 色谱分离预测: https://huggingface.co/spaces/woshixuhao/Chromatographic_Enantioseparation
- TLC Rf值预测: https://huggingface.co/spaces/woshixuhao/Rf_prediction

## 环境要求

```
Python==3.7
RDKit==2020.09.1.0
pytorch==1.11.0
pyg==2.0.4
mordred==1.2.0
pandas==1.3.5
sklearn (新增，用于分类指标计算)
```

## 主要修改内容

### 1. 任务转换
- **原始任务**: 手性色谱保留时间预测（回归，3个输出）
- **修改后任务**: 水生毒性分类（二分类，1个输出）

### 2. 数据集更换
- **原始数据**: `All_column_charity.csv` (25,869样本，包含色谱条件)
- **新数据**: `Chiral_AquaTox_scr.csv` (15,000样本，只有SMILES和毒性标签)

### 3. 模型架构调整
- **输出任务数**: `num_tasks: 3 → 1`
- **损失函数**: `MSELoss → BCEWithLogitsLoss`
- **评估指标**: `R²/MAE → Accuracy/AUC`

### 4. 特征工程简化
- **移除**: 色谱柱描述符、洗脱剂比例、流速等色谱条件特征
- **保留**: 分子图特征（原子、键、键角特征）

## 数据集信息

- **文件**: `dataset/Chiral_AquaTox_scr.csv`
- **样本数**: 15,000个手性分子
- **特征**: SMILES分子结构
- **标签**: AT（0=无毒，1=有毒）
- **标签分布**: 正样本79.9%，负样本20.1%
- **数据划分**: train(12,000), valid(1,500), test(1,500)

## 使用方法

### 1. 训练模型
```python
# 在 code/Multi_column_prediction.py 中设置
MODEL = 'Train'

# 运行训练
python code/Multi_column_prediction.py
```

### 2. 测试模型
```python
# 在 code/Multi_column_prediction.py 中设置
MODEL = 'Test'

# 运行测试
python code/Multi_column_prediction.py
```

## 文件结构

```
├── README.md                      # 项目说明文档
├── code/
│   ├── Multi_column_prediction.py # 主程序文件（已修改为AquaTox分类）
│   └── compound_tools.py          # 分子处理工具函数
├── dataset/
│   └── Chiral_AquaTox_scr.csv    # AquaTox数据集（15,000个样本）
├── saves/                         # 模型保存目录（训练时自动创建）
└── result_save/                   # 结果保存目录（预测结果保存）
```

## 模型保存和结果

- **训练模型**: `saves/model_AquaTox_classification/model_save_{epoch}.pth`
- **最佳模型**: `saves/model_AquaTox_classification/model_best.pth`
- **预测结果**: `result_save/pred_AquaTox.npy`
- **真实标签**: `result_save/true_AquaTox.npy`

## 关键参数设置

```python
MODEL = 'Train'  # 或 'Test'
Use_geometry_enhanced = True  # 使用3D几何特征
Use_column_info = False  # 不使用色谱柱信息（已移除）
```

## 预期性能

- **准确率**: 预期 > 80%
- **AUC**: 预期 > 0.85
- **训练时间**: 约1500个epoch

## 注意事项

1. **数据不平衡**: 正样本占79.9%，可能需要考虑类别权重
2. **3D构象生成**: 部分分子可能无法生成3D构象，会被自动跳过
3. **内存需求**: 15,000个分子的图数据需要较大内存
4. **GPU推荐**: 建议使用GPU加速训练

## 故障排除

1. **导入错误**: 确保安装了所有必需的包
2. **CUDA错误**: 检查PyTorch和CUDA版本兼容性
3. **内存不足**: 减小batch_size或使用更少的数据
4. **RDKit错误**: 确保RDKit版本正确且能处理SMILES

## 引用

如果使用本项目，请引用原始论文：
```
H. Xu, J. Lin, D. Zhang, F. Mo, Retention time prediction for chromatographic enantioseparation by quantile geometry-enhanced graph neural network. Nat. Commun. 14 (2023), doi:10.1038/s41467-023-38853-3.
```
