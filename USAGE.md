# 快速使用指南

## 1. 环境设置

安装必需的Python包：
```bash
pip install torch==1.11.0
pip install torch-geometric==2.0.4
pip install rdkit==2020.09.1.0
pip install mordred==1.2.0
pip install pandas==1.3.5
pip install scikit-learn
pip install tqdm
```

## 2. 训练模型

1. 打开 `code/Multi_column_prediction.py`
2. 设置 `MODEL = 'Train'`
3. 运行：
```bash
python code/Multi_column_prediction.py
```

## 3. 测试模型

1. 打开 `code/Multi_column_prediction.py`
2. 设置 `MODEL = 'Test'`
3. 运行：
```bash
python code/Multi_column_prediction.py
```

## 4. 结果查看

- 训练好的模型保存在：`saves/model_AquaTox_classification/`
- 预测结果保存在：`result_save/pred_AquaTox.npy`
- 真实标签保存在：`result_save/true_AquaTox.npy`

## 5. 参数调整

在 `code/Multi_column_prediction.py` 中可以调整的主要参数：
- `MODEL`: 'Train' 或 'Test'
- `Use_geometry_enhanced`: True（使用3D几何特征）
- 训练轮数：默认1500个epoch
- 批次大小：在args.batch_size中设置

## 缓存优化

程序已优化，避免重复处理分子数据：

- **首次运行**: 处理SMILES并生成缓存文件（耗时较长）
- **后续运行**: 直接加载缓存（快速启动）
- **缓存位置**: `dataset/processed_AquaTox_data.pkl`
- **重新缓存**: 删除缓存文件后重新运行

## 注意事项

- 确保有足够的内存处理15,000个分子
- 建议使用GPU加速训练
- 首次运行会生成3D分子构象和缓存，需要较长时间
- 切换Train/Test模式时会快速加载缓存，无需重复处理
