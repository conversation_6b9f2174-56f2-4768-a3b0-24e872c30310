import pandas as pd
import numpy as np

# 加载数据
df = pd.read_csv('dataset/Chiral_AquaTox_scr.csv')

print('=== 数据基本信息 ===')
print(f'总样本数: {len(df)}')
print(f'列名: {list(df.columns)}')
print()

print('=== AT标签分布 ===')
print(df['AT'].value_counts())
print(f'正样本比例: {df["AT"].mean():.3f}')
print()

print('=== 数据集划分 ===')
print(df['group'].value_counts())
print()

print('=== 前几个样本 ===')
print(df.head(10))
print()

print('=== SMILES长度统计 ===')
smiles_lengths = df['smiles'].str.len()
print(f'SMILES长度 - 最小值: {smiles_lengths.min()}, 最大值: {smiles_lengths.max()}, 平均值: {smiles_lengths.mean():.1f}')
print()

print('=== 检查是否有缺失值 ===')
print(df.isnull().sum())
print()

print('=== 各组中的标签分布 ===')
for group in df['group'].unique():
    group_data = df[df['group'] == group]
    print(f'{group}组: 总数={len(group_data)}, 正样本={group_data["AT"].sum()}, 正样本比例={group_data["AT"].mean():.3f}')
