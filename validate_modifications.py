#!/usr/bin/env python3
"""
Validation script to check if our modifications are correct
This script validates the code structure without running the actual training
"""

import os
import sys
import re

def check_file_exists(filepath):
    """Check if file exists"""
    if os.path.exists(filepath):
        print(f"✓ {filepath} exists")
        return True
    else:
        print(f"✗ {filepath} missing")
        return False

def check_code_modifications():
    """Check if our code modifications are correct"""
    print("=== Validating Code Modifications ===\n")
    
    # Check if main file exists
    main_file = "code/Multi_column_prediction.py"
    if not check_file_exists(main_file):
        return False
    
    # Read the modified file
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n1. Checking data loading modifications...")
    
    # Check if we're loading the correct CSV file
    if "Chiral_AquaTox_scr.csv" in content:
        print("✓ Loading correct CSV file (Chiral_AquaTox_scr.csv)")
    else:
        print("✗ Not loading correct CSV file")
    
    # Check if we're using AT labels
    if "AT_all = HPLC_all['AT'].values" in content:
        print("✓ Using AT labels for binary classification")
    else:
        print("✗ Not using AT labels")
    
    print("\n2. Checking model architecture modifications...")
    
    # Check if num_tasks is set to 1
    if "'num_tasks': 1" in content:
        print("✓ Model set to 1 task (binary classification)")
    else:
        print("✗ Model not set to 1 task")
    
    # Check if using BCEWithLogitsLoss
    if "BCEWithLogitsLoss" in content:
        print("✓ Using BCEWithLogitsLoss for binary classification")
    else:
        print("✗ Not using BCEWithLogitsLoss")
    
    print("\n3. Checking training/testing modifications...")
    
    # Check if train function is modified for binary classification
    if "criterion_fn(pred, true)" in content and "BCEWithLogitsLoss" in content:
        print("✓ Train function modified for binary classification")
    else:
        print("✗ Train function not properly modified")
    
    # Check if test function returns accuracy and AUC
    if "accuracy" in content and "auc" in content:
        print("✓ Test function returns classification metrics")
    else:
        print("✗ Test function not returning classification metrics")
    
    print("\n4. Checking data construction modifications...")
    
    # Check if Construct_dataset function is simplified
    if "def Construct_dataset(dataset, data_index, AT_labels)" in content:
        print("✓ Construct_dataset function signature updated")
    else:
        print("✗ Construct_dataset function signature not updated")
    
    # Check if column-related features are removed
    if "col_specify" not in content or "# Column-related variables removed" in content:
        print("✓ Column-related features removed")
    else:
        print("✗ Column-related features not removed")
    
    print("\n5. Checking imports...")
    
    # Check if sklearn metrics are imported
    if "from sklearn.metrics import" in content:
        print("✓ sklearn metrics imported")
    else:
        print("✗ sklearn metrics not imported")
    
    print("\n=== Validation Summary ===")
    
    # Count checks
    checks = [
        "Chiral_AquaTox_scr.csv" in content,
        "AT_all = HPLC_all['AT'].values" in content,
        "'num_tasks': 1" in content,
        "BCEWithLogitsLoss" in content,
        "accuracy" in content and "auc" in content,
        "def Construct_dataset(dataset, data_index, AT_labels)" in content,
        "from sklearn.metrics import" in content
    ]
    
    passed = sum(checks)
    total = len(checks)
    
    print(f"Passed: {passed}/{total} checks")
    
    if passed == total:
        print("✓ All modifications appear to be correct!")
        return True
    else:
        print("✗ Some modifications may be missing or incorrect")
        return False

def check_data_file():
    """Check if the data file is correct"""
    print("\n=== Validating Data File ===\n")
    
    data_file = "dataset/Chiral_AquaTox_scr.csv"
    if not check_file_exists(data_file):
        return False
    
    try:
        # Simple check without pandas, try different encodings
        encodings = ['utf-8', 'utf-8-sig', 'latin-1']
        for encoding in encodings:
            try:
                with open(data_file, 'r', encoding=encoding) as f:
                    first_line = f.readline().strip()
                    if first_line == "smiles,AT,group":
                        print(f"✓ Data file has correct header (encoding: {encoding})")

                        # Count lines
                        f.seek(0)
                        line_count = sum(1 for line in f)
                        print(f"✓ Data file has {line_count} lines")

                        return True
                    elif "smiles" in first_line and "AT" in first_line and "group" in first_line:
                        print(f"✓ Data file has correct header with BOM (encoding: {encoding})")

                        # Count lines
                        f.seek(0)
                        line_count = sum(1 for line in f)
                        print(f"✓ Data file has {line_count} lines")

                        return True
            except UnicodeDecodeError:
                continue

        print(f"✗ Data file has incorrect header or encoding issues")
        return False
    except Exception as e:
        print(f"✗ Error reading data file: {e}")
        return False

def main():
    """Main validation function"""
    print("Validating AquaTox Classification Modifications\n")
    
    # Check current directory
    if not os.path.exists("code") or not os.path.exists("dataset"):
        print("✗ Please run this script from the project root directory")
        return
    
    # Validate modifications
    code_ok = check_code_modifications()
    data_ok = check_data_file()
    
    print(f"\n=== Final Result ===")
    if code_ok and data_ok:
        print("✓ All validations passed! The modifications appear to be correct.")
        print("\nNext steps:")
        print("1. Set up the required Python environment (Python 3.7, PyTorch, RDKit, etc.)")
        print("2. Run the modified code with MODEL='Train' to train the model")
        print("3. Run with MODEL='Test' to evaluate the model")
    else:
        print("✗ Some validations failed. Please check the modifications.")

if __name__ == "__main__":
    main()
