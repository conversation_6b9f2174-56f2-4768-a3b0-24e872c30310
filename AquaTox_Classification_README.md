# AquaTox Classification using Modified GeoGNN

## 项目概述

本项目将原始的手性色谱保留时间预测模型修改为水生毒性二分类模型。使用 `Chiral_AquaTox_scr.csv` 数据集，预测分子的水生毒性（AT标签：0=无毒，1=有毒）。

## 主要修改内容

### 1. 数据加载修改
- **原始**：加载 `All_column_charity.csv`，包含保留时间、流速、洗脱剂比例等色谱条件
- **修改后**：加载 `Chiral_AquaTox_scr.csv`，只包含SMILES和AT二分类标签
- **数据集划分**：使用预定义的train/test/valid划分（12000/1500/1500）

### 2. 模型架构修改
- **原始**：3个输出任务（回归，预测保留时间区间）
- **修改后**：1个输出任务（二分类，预测毒性）
- **参数**：`num_tasks: 3 → 1`

### 3. 损失函数修改
- **原始**：MSE损失 + 分位数损失（回归）
- **修改后**：BCEWithLogitsLoss（二分类）

### 4. 评估指标修改
- **原始**：R²、MAE（回归指标）
- **修改后**：准确率、AUC（分类指标）

### 5. 特征工程修改
- **移除**：色谱柱描述符、洗脱剂比例、流速等色谱条件特征
- **保留**：分子图特征（原子特征、键特征、键角特征）

## 数据集信息

- **文件**：`dataset/Chiral_AquaTox_scr.csv`
- **样本数**：15,000个分子
- **特征**：SMILES分子结构
- **标签**：AT（0=无毒，1=有毒）
- **标签分布**：正样本79.9%，负样本20.1%
- **划分**：train(12,000), valid(1,500), test(1,500)

## 环境要求

根据原项目README：
```
Python==3.7
RDKit==2020.09.1.0
pytorch==1.11.0
pyg==2.0.4
mordred==1.2.0
pandas==1.3.5
sklearn (新增，用于分类指标计算)
```

## 使用方法

### 1. 训练模型
```python
# 在 code/Multi_column_prediction.py 中设置
MODEL = 'Train'

# 运行训练
python code/Multi_column_prediction.py
```

### 2. 测试模型
```python
# 在 code/Multi_column_prediction.py 中设置
MODEL = 'Test'

# 运行测试
python code/Multi_column_prediction.py
```

### 3. 模型保存位置
- 训练过程中的模型：`saves/model_AquaTox_classification/model_save_{epoch}.pth`
- 最佳模型：`saves/model_AquaTox_classification/model_best.pth`

### 4. 结果保存位置
- 预测结果：`result_save/pred_AquaTox.npy`
- 真实标签：`result_save/true_AquaTox.npy`

## 关键参数设置

在 `code/Multi_column_prediction.py` 中：
```python
MODEL = 'Train'  # 或 'Test'
Use_geometry_enhanced = True  # 使用3D几何特征
Use_column_info = False  # 不使用色谱柱信息
```

## 验证修改

运行验证脚本检查修改是否正确：
```bash
python validate_modifications.py
```

## 预期性能

由于这是从回归任务改为分类任务，且数据集不同，性能指标将是：
- **准确率**：预期 > 80%（基于标签分布）
- **AUC**：预期 > 0.85
- **训练时间**：约1500个epoch，具体时间取决于硬件

## 注意事项

1. **数据不平衡**：正样本占79.9%，可能需要考虑类别权重或采样策略
2. **3D构象生成**：部分分子可能无法生成3D构象，会被自动跳过
3. **内存需求**：15,000个分子的图数据需要较大内存
4. **GPU推荐**：建议使用GPU加速训练

## 文件结构

```
├── code/
│   ├── Multi_column_prediction.py  # 主要修改的文件
│   ├── compound_tools.py           # 分子处理工具
│   └── data_process.py            # 数据处理工具
├── dataset/
│   └── Chiral_AquaTox_scr.csv     # 新的数据集
├── saves/                         # 模型保存目录
├── result_save/                   # 结果保存目录
├── validate_modifications.py      # 验证脚本
└── AquaTox_Classification_README.md  # 本说明文件
```

## 故障排除

1. **导入错误**：确保安装了所有必需的包
2. **CUDA错误**：检查PyTorch和CUDA版本兼容性
3. **内存不足**：减小batch_size或使用更少的数据
4. **RDKit错误**：确保RDKit版本正确且能处理SMILES

## 后续改进建议

1. **数据增强**：考虑分子数据增强技术
2. **模型集成**：使用多个模型进行集成预测
3. **超参数优化**：使用网格搜索或贝叶斯优化
4. **类别平衡**：使用SMOTE或其他平衡技术
5. **特征选择**：分析哪些分子特征对毒性预测最重要
